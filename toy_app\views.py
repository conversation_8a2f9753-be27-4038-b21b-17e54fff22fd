from django.contrib.auth import authenticate
from django.core.files.storage import default_storage
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets, status, generics, filters
from rest_framework.generics import CreateAPIView, GenericAPIView, RetrieveUpdateAPIView
from rest_framework.parsers import MultiPartParser
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.permissions import IsAuthenticated, IsAdminUser

from .models import *
from .permissions import *
from .serializers import *
from .utils import send_email_verification_code


# Create your views here.


# Токен с пользователем
class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


# Подробная информация о пользователе
class UserProfile(RetrieveUpdateAPIView):
    serializer_class = UserSelfUpdateSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user



class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        serializer = ChangePasswordSerializer(data=request.data)

        if serializer.is_valid():
            old_password = serializer.validated_data['old_password']
            new_password = serializer.validated_data['new_password']

            if not user.check_password(old_password):
                return Response({"error": "Неверный текущий пароль."}, status=status.HTTP_400_BAD_REQUEST)

            user.set_password(new_password)
            user.save()
            return Response({"detail": "Пароль успешно изменён."}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




# RegisterView
class RegisterView(CreateAPIView):
    serializer_class = RegisterSerializer


# Для получения верификации кода
class VerifyCodeView(GenericAPIView):
    serializer_class = VerifyCodeSerializer

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response({'detail': 'Почта подтверждена. Аккаунт активирован!'})


class ResendCodeView(APIView):
    def post(self, request):
        serializer = ResendCodeSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.context['user']

            # удалим старые коды и отправим новый
            EmailVerificationCode.objects.filter(user=user).delete()
            send_email_verification_code(user)

            return Response({"detail": "Verification code resent"}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


"""
    Verification code list for admin
"""
class EmailVerificationCodeViewSet(viewsets.ModelViewSet):
    queryset = EmailVerificationCode.objects.all().select_related('user')
    serializer_class = EmailVerificationCodeSerializer
    permission_classes = [IsAdminUser]  # доступ только для админа



class UserList(generics.ListAPIView):
    queryset = User.objects.all()
    serializer_class = UserDetailSerializer
    permission_classes = [IsStaffOrReadOnly]

    # ✅ Подключаем фильтрацию и поиск
    filter_backends = [
        DjangoFilterBackend,
        filters.OrderingFilter,
        filters.SearchFilter,
    ]

    # ✅ Разрешаем фильтровать по этим полям (через query-параметры)
    filterset_fields = ['is_active', 'is_staff', 'email', 'phone']

    # ✅ Разрешаем искать по этим полям
    search_fields = ['email', 'phone']

    # ✅ Сортировка по этим полям
    ordering_fields = ['id', 'date_joined', 'last_login', 'email']
    ordering = ['-date_joined']  # по умолчанию



"""
    Изменения пользователя
"""
class AdminUserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = AdminUserUpdateSerializer
    permission_classes = [IsAdminUser]

    def destroy(self, request, *args, **kwargs):
        user = self.get_object()
        if user.is_superuser:
            return Response(
                {"error": "Нельзя удалить суперпользователя."},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().destroy(request, *args, **kwargs)




class GameViewSet(viewsets.ModelViewSet):
    queryset = Game.objects.all().order_by('-created_at')
    serializer_class = GameSerializer
    permission_classes = [IsStaffOrReadOnly]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request  # 👈 добавили request в контекст
        return context


# CART
class CartItemViewSet(viewsets.ModelViewSet):
    serializer_class = CartItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CartItem.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except serializers.ValidationError as e:
            return Response({'detail': e.detail}, status=400)


# Просмотр игр пользователя
class UserLibraryListView(generics.ListAPIView):
    serializer_class = UserLibrarySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserLibrary.objects.filter(user=self.request.user).select_related('game')


# ручное добавление
class AddToLibraryView(generics.CreateAPIView):
    serializer_class = AddToLibrarySerializer
    permission_classes = [IsAdminUser]


class GameKeyViewSet(viewsets.ModelViewSet):
    queryset = GameKey.objects.all().select_related('game', 'assigned_to_user')
    serializer_class = GameKeySerializer
    permission_classes = [IsAdminOrOwner]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return GameKey.objects.all()
        return GameKey.objects.filter(assigned_to_user=user)


"""
    Проверка на покупку из корзины <UNK> <UNK> <UNK>
"""
class CheckoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        cart_items = CartItem.objects.filter(user=user)

        if not cart_items.exists():
            return Response({'detail': 'Корзина пуста.'}, status=status.HTTP_400_BAD_REQUEST)

        purchases = []
        skipped = []
        now = timezone.now()

        for item in cart_items:
            # 1) Если это одиночная игра
            if item.game:
                game = item.game

                access_active = UserGameAccess.objects.filter(
                    user=user,
                    game=game,
                    access_start__lte=now,
                    access_end__gte=now
                ).exists()

                if access_active:
                    skipped.append({
                        'type': 'game',
                        'id': game.id,
                        'title': game.title,
                        'reason': 'Уже есть активный доступ'
                    })
                    continue

                purchase = Purchase.objects.create(
                    user=user,
                    game=game,
                    package=None,
                    purchase_type='game',
                    price=game.price,
                    status='pending'
                )
                purchases.append(purchase)

            # 2) Если это пакет
            elif item.game_package:
                package = item.game_package

                # Здесь можно проверить, не покупал ли пользователь этот пакет ранее
                # или не активирован ли у него доступ ко всем играм пакета.
                # Для простоты — не дублируем, просто создаём покупку.
                purchase = Purchase.objects.create(
                    user=user,
                    game=None,
                    package=package,
                    purchase_type='package',
                    price=package.price,
                    status='pending'
                )
                purchases.append(purchase)

        # Очистить корзину
        cart_items.delete()

        if not purchases:
            return Response({
                'detail': 'Нет новых покупок (все игры уже активны).',
                'skipped': skipped
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = PurchaseSerializer(purchases, many=True)
        return Response({
            'purchases': serializer.data,
            'skipped': skipped
        }, status=status.HTTP_201_CREATED)


class PurchaseViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = PurchaseSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Purchase.objects.filter(user=self.request.user).order_by('-created_at')


"""
    Покупка фейковая
"""
class PurchasePaymentView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        try:
            original_purchase = Purchase.objects.get(pk=pk, user=request.user)
        except Purchase.DoesNotExist:
            return Response({'error': 'Покупка не найдена.'}, status=404)

        access_type = request.data.get('access_type')
        if access_type not in ['oneday', 'subscription']:
            return Response({'error': 'Неверный тип доступа.'}, status=400)

        now = timezone.now()

        # Проверка: если покупка оплачена — доступ ещё активен?
        if original_purchase.status == 'paid':
            access_active = UserGameAccess.objects.filter(
                user=request.user,
                game=original_purchase.game,
                access_start__lte=now,
                access_end__gte=now
            ).exists()

            if access_active:
                return Response({'detail': 'Доступ к игре уже активен.'}, status=400)

            # 👉 доступ истёк, создаём новую покупку
            new_purchase = Purchase.objects.create(
                user=request.user,
                game=original_purchase.game,
                purchase_type='renewal',
                price=original_purchase.price,
                status='pending',
            )

            purchase = new_purchase
        else:
            # обычная неоплаченная покупка
            purchase = original_purchase

        # 💳 Создаём фейковый платёж
        Payment.objects.create(
            user=request.user,
            purchase=purchase,
            amount=purchase.price,
            status='success',
            provider='other',
        )

        if access_type == 'subscription':
            access_start = now
            access_end = now + timedelta(days=30)
            activated = True
        else:  # oneday
            activated = False
            access_start = None
            access_end = None

        UserGameAccess.objects.create(
            user=request.user,
            game=purchase.game,
            access_type=access_type,
            access_start=access_start,
            access_end=access_end,
            activated=activated
        )

        # 🎮 Добавляем в библиотеку, если ещё не добавлен
        UserLibrary.objects.get_or_create(user=request.user, game=purchase.game)

        # ✅ Обновляем статус покупки
        purchase.status = 'paid'
        purchase.save()

        return Response({
            'detail': 'Доступ создан (активный — если subscription).',
            'purchase_id': purchase.id
        }, status=200)



"""
    Ручная активация для oneday
"""

class ActivateAccessView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        game_id = request.data.get('game_id')
        access_start_str = request.data.get('access_start')

        # Проверка наличия необходимых полей
        if not game_id:
            return Response({'error': 'Поле game_id обязательно.'}, status=status.HTTP_400_BAD_REQUEST)
        if not access_start_str:
            return Response({'error': 'Поле access_start обязательно.'}, status=status.HTTP_400_BAD_REQUEST)

        # Парсинг времени старта
        try:
            access_start = timezone.datetime.fromisoformat(access_start_str)
            if timezone.is_naive(access_start):
                access_start = timezone.make_aware(access_start, timezone.get_current_timezone())
        except ValueError:
            return Response({'error': 'Неверный формат даты. Используй ISO 8601 (например: 2025-07-15T19:30:00)'}, status=status.HTTP_400_BAD_REQUEST)

        # Поиск подходящего доступа
        try:
            access = UserGameAccess.objects.get(
                user=request.user,
                game_id=game_id,
                access_type='oneday',
                activated=False
            )
        except UserGameAccess.DoesNotExist:
            return Response({'error': 'Не найден неактивированный доступ.'}, status=status.HTTP_404_NOT_FOUND)

        # Активация доступа
        access.access_start = access_start
        access.access_end = access_start + timedelta(days=1)
        access.activated = True
        access.save()

        return Response({
            'detail': 'Однодневный доступ активирован.',
            'access_start': access.access_start.isoformat(),
            'access_end': access.access_end.isoformat()
        }, status=status.HTTP_200_OK)



"""
    Просмотр статистки библиотеки и корзины
"""

class UserSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        cart_count = user.cart_items.count()
        library_count = user.library.count()
        pending_payments_count = Purchase.objects.filter(user=user, status='pending').count()

        return Response({
            'cart_count': cart_count,
            'library_count': library_count,
            'pending_payments_count': pending_payments_count,
        })


class UploadGalleryImageView(APIView):
    parser_classes = [MultiPartParser]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        file = request.FILES.get('file')
        if not file:
            return Response({"error": "Файл не найден"}, status=status.HTTP_400_BAD_REQUEST)

        path = default_storage.save(f'game_gallery/{file.name}', file)
        url = default_storage.url(path)

        return Response({"url": url}, status=status.HTTP_201_CREATED)


class GameGalleryItemViewSet(viewsets.ModelViewSet):
    queryset = GameGalleryItem.objects.all()
    serializer_class = GameGalleryItemSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save()


class UserGameAccessListView(generics.ListCreateAPIView):
    serializer_class = UserGameAccessSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return UserGameAccess.objects.filter(user=self.request.user)


"""
    Админ просматривает доступы
"""

class AdminUserGameAccessViewSet(viewsets.ModelViewSet):
    queryset = UserGameAccess.objects.all().select_related('user', 'game')
    serializer_class = AdminUserGameAccess
    permission_classes = [IsAdminUser]


class GameAccessAuthView(APIView):
    def post(self, request):
        user_code = request.data.get('user_code')
        game_code = request.data.get('game_code')

        # 🔐 Проверка параметров
        if not user_code or not game_code:
            return Response(
                {'error': 'user_code и game_code обязательны.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 🔎 Найти пользователя
        try:
            user = User.objects.get(user_code=user_code)
        except User.DoesNotExist:
            return Response({'error': 'Пользователь не найден.'}, status=status.HTTP_404_NOT_FOUND)

        # 🔎 Найти игру по уникальному коду
        try:
            game = Game.objects.get(game_code=game_code)
        except Game.DoesNotExist:
            return Response({'error': 'Игра не найдена.'}, status=status.HTTP_404_NOT_FOUND)

        # ⏳ Проверить доступ по времени
        now = timezone.now()
        access = UserGameAccess.objects.filter(
            user=user,
            game=game,
            access_start__lte=now,
            access_end__gte=now
        ).order_by('-access_end').first()

        return Response({
            'has_access': access is not None,
            'access_end': access.access_end if access else None
        }, status=200)





"""
    Пакет игр список вьюшка
"""
class GamePackageViewSet(viewsets.ModelViewSet):
    queryset = GamePackage.objects.all()
    serializer_class = GamePackageSerializer
    permission_classes = [IsStaffOrReadOnly]


class GameFileViewSet(viewsets.ModelViewSet):
    queryset = GameFile.objects.all().select_related('game')
    serializer_class = GameFileSerializer
    permission_classes = [IsStaffOrReadOnly]

    def get_queryset(self):
        user = self.request.user
        queryset = GameFile.objects.select_related('game')

        # Если пользователь не админ, показываем только файлы игр, к которым у него есть доступ
        if not user.is_staff:
            if not user.is_authenticated:
                return queryset.none()

            now = timezone.now()
            accessible_games = UserGameAccess.objects.filter(
                user=user,
                access_start__lte=now,
                access_end__gte=now
            ).values_list('game_id', flat=True)

            queryset = queryset.filter(game_id__in=accessible_games, is_active=True)

        return queryset.order_by('-uploaded_at')


class BindGameFileView(APIView):
    """
    API для привязки файла игры к игре (простой способ создания связи)
    """
    permission_classes = [IsAdminUser]

    def post(self, request):
        game_id = request.data.get('game_id')
        file_name = request.data.get('file_name')
        file_path = request.data.get('file_path')
        platform = request.data.get('platform')
        version = request.data.get('version', '1.0')
        file_size = request.data.get('file_size')
        description = request.data.get('description', '')

        # Проверка обязательных полей
        if not all([game_id, file_name, file_path, platform]):
            return Response(
                {'error': 'game_id, file_name, file_path и platform обязательны.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Проверка существования игры
        try:
            game = Game.objects.get(id=game_id)
        except Game.DoesNotExist:
            return Response(
                {'error': 'Игра не найдена.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Создание файла игры
        try:
            game_file = GameFile.objects.create(
                game=game,
                file_name=file_name,
                file_path=file_path,
                platform=platform,
                version=version,
                file_size=file_size,
                description=description
            )

            serializer = GameFileSerializer(game_file)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': f'Ошибка при создании файла игры: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )