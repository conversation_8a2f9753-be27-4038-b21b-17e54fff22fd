from rest_framework import serializers
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import serializers
from django.contrib.auth import password_validation
from .utils import send_email_verification_code
from .models import *

User = get_user_model()


# register
class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        data.update({
            'user_id': self.user.id,
            'email': self.user.email,
            'user_code': self.user.user_code,
            'is_staff': self.user.is_staff,
        })
        return data


class RegisterSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        if User.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError("Email already taken")
        return value

    def create(self, validated_data):
        email = validated_data['email']
        password = validated_data['password']

        try:
            user = User.objects.get(email=email)

            # Пользователь не активен → обновим пароль, заново отправим код
            user.set_password(password)
            user.save()

        except User.DoesNotExist:
            # Новый пользователь
            user = User.objects.create_user(
                email=email,
                password=password,
                is_active=False
            )

        # Очистим предыдущие коды и отправим новый
        EmailVerificationCode.objects.filter(user=user).delete()
        send_email_verification_code(user)

        return user


class VerifyCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()
    code = serializers.CharField(max_length=6)

    def validate(self, data):
        email = data['email']
        code = data['code']

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError("Пользователь не найден")

        try:
            code_entry = EmailVerificationCode.objects.filter(
                user=user,
                code=code,
                is_used=False
            ).latest('created_at')
        except EmailVerificationCode.DoesNotExist:
            raise serializers.ValidationError("Неверный код")

        if code_entry.is_expired():
            raise serializers.ValidationError("Код истёк")

        # Активируем пользователя
        user.is_active = True
        user.save()

        code_entry.is_used = True
        code_entry.save()

        return data



"""
    For viewing email verifications all list
"""
class EmailVerificationCodeSerializer(serializers.ModelSerializer):
    user_email = serializers.ReadOnlyField(source='user.email')

    class Meta:
        model = EmailVerificationCode
        fields = ['id', 'user', 'user_email', 'code', 'created_at' , 'is_used']
        read_only_fields = ['created_at']



class ResendCodeSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this email does not exist")

        if user.is_active:
            raise serializers.ValidationError("User already verified")

        self.context['user'] = user  # передаём user во view через контекст
        return value



# game image
class GameGalleryItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameGalleryItem
        fields = ['id', 'game', 'file', 'uploaded_at']
        read_only_fields = ['id', 'uploaded_at']


class GameShortSerializer(serializers.ModelSerializer):
    class Meta:
        model = Game
        fields = ['id', 'title']  # добавь ещё поля, если нужно


# game
class GameSerializer(serializers.ModelSerializer):
    is_in_cart = serializers.SerializerMethodField()
    is_in_library = serializers.SerializerMethodField()
    gallery_items = GameGalleryItemSerializer(many=True, read_only=True)
    has_access = serializers.SerializerMethodField()
    has_unactivated_access = serializers.SerializerMethodField()
    access_end = serializers.SerializerMethodField()

    class Meta:
        model = Game
        fields = [
            'id',
            'title',
            'game_code',
            'subtitle',
            'description',
            'how_to_play',
            'target_audience',
            'requires_device',
            'price',
            'trial_available',
            'cover_image',
            'system_requirements',
            'required_equipment',
            'created_at',
            'is_in_cart',
            'is_in_library',
            'has_access',
            'access_end',
            'has_unactivated_access',
            'gallery_items',
        ]
        read_only_fields = ['id', 'created_at']

    def get_is_in_cart(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False
        return CartItem.objects.filter(user=user, game=obj).exists()

    def get_is_in_library(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False
        return UserLibrary.objects.filter(user=user, game=obj).exists()

    def get_access_end(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return None

        now = timezone.now()
        access = UserGameAccess.objects.filter(
            user=user,
            game=obj,
            access_start__lte=now,
            access_end__gte=now
        ).order_by('-access_end').first()

        if not access:
            return None

        return access.access_end

    def get_has_access(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False

        now = timezone.now()
        return UserGameAccess.objects.filter(
            user=user,
            game=obj,
            access_start__lte=now,
            access_end__gte=now
        ).exists()

    def get_has_unactivated_access(self, obj):
        user = self.context['request'].user
        if not user.is_authenticated:
            return False

        return UserGameAccess.objects.filter(
            user=user,
            game=obj,
            activated=False
        ).exists()



"""
    Пакет игр 
"""
class GamePackageSerializer(serializers.ModelSerializer):
    games = GameShortSerializer(many=True, read_only=True)
    game_ids = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Game.objects.all(), write_only=True
    )

    class Meta:
        model = GamePackage
        fields = ['id', 'name', 'description', 'price', 'benefit_1', 'benefit_2', 'benefit_3',
                  'duration_days', 'games', 'game_ids', 'max_selectable_games']

    def create(self, validated_data):
        games = validated_data.pop('game_ids')
        package = GamePackage.objects.create(**validated_data)
        package.games.set(games)
        return package




class CartItemSerializer(serializers.ModelSerializer):
    game = serializers.PrimaryKeyRelatedField(queryset=Game.objects.all(), required=False, allow_null=True)
    game_package = serializers.PrimaryKeyRelatedField(queryset=GamePackage.objects.all(), required=False, allow_null=True)


    game_obj = GameSerializer(source='game', read_only=True)
    game_package_obj = GamePackageSerializer(source='game_package', read_only=True)


    class Meta:
        model = CartItem
        fields = ['id', 'user', 'game', 'game_obj', 'game_package', 'game_package_obj', 'quantity', 'added_at',]
        read_only_fields = ['user', 'added_at']




    def validate(self, data):
        user = self.context['request'].user
        game = data.get('game')
        package = data.get('game_package')

        if not game and not package:
            raise serializers.ValidationError("Нужно указать либо игру, либо пакет.")

        if game and package:
            raise serializers.ValidationError("Нельзя добавить и игру, и пакет одновременно.")

        if game:
            if CartItem.objects.filter(user=user, game=game).exists():
                raise serializers.ValidationError("Игра уже в корзине.")
            now = timezone.now()
            if UserGameAccess.objects.filter(user=user, game=game, access_start__lte=now, access_end__gte=now).exists():
                raise serializers.ValidationError("У вас уже есть активный доступ к этой игре.")

        if package:
            if CartItem.objects.filter(user=user, game_package=package).exists():
                raise serializers.ValidationError("Пакет уже в корзине.")

        return data




# user detail serializer
class UserDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'phone',
            'is_active',
            'is_staff',
            'is_superuser',
            'user_code',
            'date_joined',
            'last_login',
        ]
        read_only_fields = fields


"""
    Изменения данных для пользователя
"""
class UserSelfUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'email', 'phone', 'is_staff', 'user_code']
        read_only_fields = ['id', 'email', 'is_staff', 'user_code']


"""
    Изменить пароль
"""
class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate_new_password(self, value):
        password_validation.validate_password(value)
        return value

"""
    Полное изменение списка пользователей.
"""
class AdminUserUpdateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'id',
            'email',
            'phone',
            'is_active',
            'is_staff',
            'is_superuser',
            'user_code',
            'date_joined',
            'last_login',
            'password',
        ]
        read_only_fields = ['date_joined', 'last_login']

    def update(self, instance, validated_data):
        validated_data.pop('is_superuser', None)
        password = validated_data.pop('password', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance






# serializers.py
class UserLibrarySerializer(serializers.ModelSerializer):
    game = GameSerializer(read_only=True)
    access_end = serializers.SerializerMethodField()

    class Meta:
        model = UserLibrary
        fields = ['id', 'game', 'added_at', 'access_end']

    def get_access_end(self, obj):
        user = self.context['request'].user
        now = timezone.now()

        access = UserGameAccess.objects.filter(
            user=user,
            game=obj.game,
            access_start__lte=now,
            access_end__gte=now
        ).order_by('-access_end').first()

        if not access:
            return None

        return access.access_end


# Добавление игры в библиотеку (ручная)
class AddToLibrarySerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField()
    game_id = serializers.IntegerField()

    class Meta:
        model = UserLibrary
        fields = ['user_id', 'game_id']

    def validate(self, attrs):
        user_id = attrs['user_id']
        game_id = attrs['game_id']

        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise serializers.ValidationError({'user_id': 'Пользователь не найден.'})

        try:
            game = Game.objects.get(id=game_id)
        except Game.DoesNotExist:
            raise serializers.ValidationError({'game_id': 'Игра не найдена.'})

        if UserLibrary.objects.filter(user=user, game=game).exists():
            raise serializers.ValidationError('Эта игра уже есть в библиотеке пользователя.')

        attrs['user'] = user
        attrs['game'] = game
        return attrs

    def create(self, validated_data):
        return UserLibrary.objects.create(
            user=validated_data['user'],
            game=validated_data['game']
        )


# Сериализатор для ключей игровых
class GameKeySerializer(serializers.ModelSerializer):
    game_title = serializers.CharField(source='game.title', read_only=True)
    user_email = serializers.EmailField(source='assigned_to_user.email', read_only=True)

    class Meta:
        model = GameKey
        fields = [
            'id',
            'game',
            'game_title',
            'code',
            'is_used',
            'assigned_to_user',
            'user_email',
            'assigned_at',
            'expires_at',
        ]
        read_only_fields = ['is_used', 'assigned_at', 'assigned_to_user']


"""
    Покупка для пользователя
"""


class PurchaseSerializer(serializers.ModelSerializer):
    game_title = serializers.CharField(source='game.title', read_only=True)
    package_title = serializers.CharField(source='package.name', read_only=True)

    class Meta:
        model = Purchase
        fields = ['id', 'game_title', 'purchase_type', 'package_title',
                  'price', 'status', 'created_at']


class UserGameAccessSerializer(serializers.ModelSerializer):
    has_access = serializers.SerializerMethodField()
    game_title = serializers.CharField(source='game.title', read_only=True)

    class Meta:
        model = UserGameAccess
        fields = ['id', 'game', 'game_title', 'access_type', 'access_start', 'access_end', 'has_access' , 'activated']

    def get_has_access(self, obj):
        return obj.has_access()



"""
    Для админки проверка доступов у игроков
"""
class AdminUserGameAccess(serializers.ModelSerializer):
    has_access = serializers.SerializerMethodField(read_only=True)
    user_email = serializers.EmailField(source='user.email', read_only=True)
    game_title = serializers.CharField(source='game.title', read_only=True)

    class Meta:
        model = UserGameAccess
        fields = [
            'id',
            'user',
            'user_email',
            'game',
            'game_title',
            'access_type',
            'access_start',
            'access_end',
            'has_access',
            'activated'
        ]

    def get_has_access(self, obj):
        return obj.has_access()





